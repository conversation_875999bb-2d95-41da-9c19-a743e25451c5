using Microsoft.Extensions.Logging;
using Moq;
using PharmaLex.VigiLit.ContractManagement.Ui.Journals;
using PharmaLex.VigiLit.Domain.Models;
using PharmaLex.VigiLit.Scraping.Service.Clients;
using PharmaLex.VigiLit.Scraping.Service.Interfaces;
using PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Builders;

namespace PharmaLex.VigiLit.Scraping.Service.Unit.Tests.Services;

public class VigiLitScrapingClientTests
{
    private readonly Mock<IJournalRepository> _journalRepositoryMock;
    private readonly Mock<ILogger<VigiLitScrapingClient>> _loggerMock;
    private readonly Mock<IApifyTaskService> _taskServiceMock;
    private readonly Mock<IApifyScheduleService> _scheduleServiceMock;
    private readonly Mock<IApifyWebhookService> _webhookServiceMock;
    private readonly Mock<IScrapingConfigurationService> _configurationServiceMock;
    private readonly VigiLitScrapingClient _sut;

    public VigiLitScrapingClientTests()
    {
        _journalRepositoryMock = new Mock<IJournalRepository>();
        _loggerMock = new Mock<ILogger<VigiLitScrapingClient>>();
        _taskServiceMock = new Mock<IApifyTaskService>();
        _scheduleServiceMock = new Mock<IApifyScheduleService>();
        _webhookServiceMock = new Mock<IApifyWebhookService>();
        _configurationServiceMock = new Mock<IScrapingConfigurationService>();

        _sut = new VigiLitScrapingClient(
            _journalRepositoryMock.Object,
            _loggerMock.Object,
            _taskServiceMock.Object,
            _scheduleServiceMock.Object,
            _webhookServiceMock.Object,
            _configurationServiceMock.Object);
    }

    [Fact]
    public async Task RestoreSchedulesAsync_WhenNoEnabledJournalsWithSchedules_ReturnsEmptyResult()
    {
        // Arrange
        var journals = new List<Journal>
        {
            new JournalBuilder().WithName("Journal 1").WithEnabled(false).WithCronExpression("0 9 * * 1").Build(),
            new JournalBuilder().WithName("Journal 2").WithEnabled(true).WithCronExpression("").Build(),
            new JournalBuilder().WithName("Journal 3").WithEnabled(true).WithCronExpression("").Build()
        };

        _journalRepositoryMock.Setup(x => x.GetAll()).ReturnsAsync(journals);

        // Act
        var result = await _sut.Send();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Empty(result.Errors);
        Assert.Contains("No enabled journals with schedules found", result.Messages);
    }

    [Fact]
    public async Task RestoreSchedulesAsync_WhenWebhookUrlNotConfigured_ReturnsError()
    {
        // Arrange
        var journals = new List<Journal>
        {
            new JournalBuilder().WithName("Journal 1").WithEnabled(true).WithCronExpression("0 9 * * 1").Build()
        };

        _journalRepositoryMock.Setup(x => x.GetAll()).ReturnsAsync(journals);
        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns((string)null!);

        // Act
        var result = await _sut.RestoreSchedulesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Webhook URL not configured", result.Errors);
        Assert.Empty(result.Messages);
    }

    [Fact]
    public async Task RestoreSchedulesAsync_WhenAllOperationsSucceed_ReturnsSuccessResult()
    {
        // Arrange
        var journals = new List<Journal>
        {
            new JournalBuilder().WithName("Nature Medicine").WithEnabled(true).WithCronExpression("0 9 * * 1").WithUrl("https://nature.com/nm").Build(),
            new JournalBuilder().WithName("The Lancet").WithEnabled(true).WithCronExpression("0 10 * * 2").WithUrl("https://thelancet.com").Build()
        };

        _journalRepositoryMock.Setup(x => x.GetAll()).ReturnsAsync(journals);
        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<Journal>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("task-123");

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.RestoreSchedulesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(2, result.JournalsProcessed);
        Assert.Equal(2, result.TasksCreated);
        Assert.Equal(2, result.SchedulesCreated);
        Assert.Equal(2, result.WebhooksCreated);
        Assert.Empty(result.Errors);
        Assert.Equal(6, result.Messages.Count);

        _taskServiceMock.Verify(x => x.CreateTaskForJournalAsync(It.IsAny<Journal>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Exactly(2));
        _webhookServiceMock.Verify(x => x.CreateWebhookForTaskAsync("task-123", "https://localhost:5001/webhook", It.IsAny<CancellationToken>()), Times.Exactly(2));
    }

    [Fact]
    public async Task RestoreSchedulesAsync_WhenTaskCreationFails_ReturnsError()
    {
        // Arrange
        var journals = new List<Journal>
        {
            new JournalBuilder().WithName("Nature Medicine").WithEnabled(true).WithCronExpression("0 9 * * 1").WithUrl("https://nature.com/nm").Build()
        };

        _journalRepositoryMock.Setup(x => x.GetAll()).ReturnsAsync(journals);
        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<Journal>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((string)null!);

        // Act
        var result = await _sut.RestoreSchedulesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(1, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("Failed to create task for journal 'Nature Medicine': Task ID was null or empty", result.Errors);

        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
        _webhookServiceMock.Verify(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()), Times.Never);
    } 

    [Fact]
    public async Task RestoreSchedulesAsync_WhenGeneralExceptionOccurs_ReturnsError()
    {
        // Arrange
        _journalRepositoryMock.Setup(x => x.GetAll()).ThrowsAsync(new Exception("Database connection failed"));

        // Act
        var result = await _sut.RestoreSchedulesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(0, result.JournalsProcessed);
        Assert.Equal(0, result.TasksCreated);
        Assert.Equal(0, result.SchedulesCreated);
        Assert.Equal(0, result.WebhooksCreated);
        Assert.Contains("General error: Database connection failed", result.Errors);
    }

    [Fact]
    public async Task RestoreSchedulesAsync_GroupsJournalsByCronExpression()
    {
        // Arrange
        var journals = new List<Journal>
        {
            new JournalBuilder().WithName("Journal 1").WithEnabled(true).WithCronExpression("0 9 * * 1").WithUrl("https://journal1.com").Build(),
            new JournalBuilder().WithName("Journal 2").WithEnabled(true).WithCronExpression("0 9 * * 1").WithUrl("https://journal2.com").Build(), // Same cron
            new JournalBuilder().WithName("Journal 3").WithEnabled(true).WithCronExpression("0 10 * * 2").WithUrl("https://journal3.com").Build()
        };

        _journalRepositoryMock.Setup(x => x.GetAll()).ReturnsAsync(journals);
        _configurationServiceMock.Setup(x => x.GetWebhookUrl()).Returns("https://localhost:5001/webhook");

        _taskServiceMock.Setup(x => x.CreateTaskForJournalAsync(It.IsAny<Journal>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync("task-123");

        _scheduleServiceMock.Setup(x => x.CreateScheduleForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _webhookServiceMock.Setup(x => x.CreateWebhookForTaskAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        // Act
        var result = await _sut.RestoreSchedulesAsync();

        // Assert
        Assert.NotNull(result);
        Assert.Equal(3, result.JournalsProcessed);
        Assert.Equal(3, result.TasksCreated);
        Assert.Equal(3, result.SchedulesCreated);
        Assert.Equal(3, result.WebhooksCreated);
        Assert.Empty(result.Errors);

        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), "0 9 * * 1", It.IsAny<CancellationToken>()), Times.Exactly(2));
        _scheduleServiceMock.Verify(x => x.CreateScheduleForTaskAsync("task-123", It.IsAny<string>(), "0 10 * * 2", It.IsAny<CancellationToken>()), Times.Once);
    }

}

using PharmaLex.VigiLit.Scraping.Client;
using PharmaLex.VigiLit.Scraping.Service.Services;

namespace PharmaLex.VigiLit.Scraping.Service.Clients;

public class VigiLitScrapingClient : IVigiLitScrapingClient
{
    private readonly IRestoreSchedulesCommandHandler _restoreSchedulesHandler;

    public VigiLitScrapingClient(IRestoreSchedulesCommandHandler restoreSchedulesHandler)
    {
        _restoreSchedulesHandler = restoreSchedulesHandler;
    }

    public async Task Send()
    {
        await _restoreSchedulesHandler.Consume();
    }

}
